import { StatusEnum } from '../types'

export interface IHistoricalImpactStatus {
  getKeyAchievementStatus: StatusEnum
  getHistoricalImpactStatus: StatusEnum
  addHistoricalImpactStatus: StatusEnum
  deleteHistoricalImpactStatus: StatusEnum
  updateHistoricalImpactStatus: StatusEnum
  HistoricalImpactSortStatus: StatusEnum
  keyAchievements: any[]
  keyAchievement: any
  formData: any
}

export interface IGetHistoricalImpactResponse {}
export interface IHistoricalImpact {}
export interface IHistoricalImpactsStatus {}
